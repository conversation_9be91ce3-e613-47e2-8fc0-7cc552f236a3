# TradeUtils 使用说明

## 概述

`trade_utils.py` 提供了两种使用交易工具的方式：

1. **类装饰器方式** (推荐) - 使用 `@trade_utils` 装饰器
2. **传统实例化方式** - 创建 `TradeUtils` 实例

## 1. 类装饰器方式 (推荐)

### 使用方法

```python
from strategies.trade_utils import trade_utils

@trade_utils
class YourStrategy(CtaTemplate):
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        # 装饰器会自动添加 trade_cash 和 trade_total_pos 属性
        
    def on_bar(self, bar: BarData) -> None:
        # 直接调用交易方法，无需创建实例
        if some_condition:
            self.buy_share(bar.close_price, 0.5)  # 买入50%仓位
        if some_other_condition:
            self.sell_share(bar.close_price, 1.0)  # 全部卖出
            
    def on_trade(self, trade: TradeData) -> None:
        # 更新资金
        rate = self.cta_engine.rate
        if trade.direction == Direction.LONG:
            self.trade_cash -= trade.price * trade.volume * (1 + rate)
        else:
            self.trade_cash += trade.price * trade.volume * (1 - rate)
```

### 装饰器提供的方法和属性

**属性：**
- `self.trade_cash`: 当前可用资金
- `self.trade_total_pos`: 总持仓量

**方法：**
- `self.calc_target_share(close_price, rate, use_cash=True)`: 计算目标交易量
- `self.buy_share(close_price, rate)`: 按比例买入
- `self.sell_share(close_price, rate, use_total_pos=False)`: 按比例卖出

## 2. 传统实例化方式

### 使用方法

```python
from strategies.trade_utils import TradeUtils

class YourStrategy(CtaTemplate):
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        self.trade_utils = TradeUtils(self)
        
    def on_bar(self, bar: BarData) -> None:
        if some_condition:
            self.trade_utils.buy_share(bar.close_price, 0.5)
        if some_other_condition:
            self.trade_utils.sell_share(bar.close_price, 1.0)
            
    def on_trade(self, trade: TradeData) -> None:
        # 更新资金
        rate = self.cta_engine.rate
        if trade.direction == Direction.LONG:
            self.trade_utils.cash -= trade.price * trade.volume * (1 + rate)
        else:
            self.trade_utils.cash += trade.price * trade.volume * (1 - rate)
```

## 方法说明

### calc_target_share(close_price, rate, use_cash=True)
根据资金按比例计算交易量

**参数：**
- `close_price`: 当前价格
- `rate`: 交易比例 (0-1)
- `use_cash`: 是否使用当前资金计算 (默认True)

### buy_share(close_price, rate)
根据当前资金按比例买入

**参数：**
- `close_price`: 买入价格
- `rate`: 买入比例 (0-1)

### sell_share(close_price, rate, use_total_pos=False)
根据持有按比例卖出

**参数：**
- `close_price`: 卖出价格
- `rate`: 卖出比例 (0-1)
- `use_total_pos`: 是否按总持仓计算 (默认False)

## 优势对比

### 类装饰器方式的优势：
1. **更简洁**: 无需创建额外的实例
2. **更直观**: 方法直接属于策略类
3. **更高效**: 减少了一层方法调用
4. **更易维护**: 代码结构更清晰

### 传统方式的优势：
1. **向后兼容**: 不需要修改现有代码
2. **更明确**: 明确显示使用了交易工具

## 迁移指南

如果要从传统方式迁移到装饰器方式：

1. 在类定义前添加 `@trade_utils` 装饰器
2. 删除 `self.trade_utils = TradeUtils(self)` 这行
3. 将 `self.trade_utils.method_name()` 改为 `self.method_name()`
4. 将 `self.trade_utils.cash` 改为 `self.trade_cash`
5. 将 `self.trade_utils.total_pos` 改为 `self.trade_total_pos`

## 示例

参考 `example_decorated_strategy.py` 文件查看完整的使用示例。
