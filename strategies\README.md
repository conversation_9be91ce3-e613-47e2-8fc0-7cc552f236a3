创建软链接，方便修改策略

```
mklink /J C:\Users\<USER>\strategies E:\workspace\python\trade\vnpy\strategies
```

## 文档

1. [策略开发](https://www.vnpy.com/docs/cn/community/app/cta_strategy.html#cta-ctatemplate)

## 开发指南

```python
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)
class DoubleMaStrategy2(CtaTemplate):
    author = "用Python的交易员"

    boll_window = 18
    boll_dev = 3.4
    cci_window = 10
    atr_window = 30
    sl_multiplier = 5.2
    fixed_size = 1

    boll_up = 0
    boll_down = 0
    cci_value = 0
    atr_value = 0

    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0
    #参数，固定的（由交易员从外部指定）
    parameters = [
        "boll_window",
        "boll_dev",
        "cci_window",
        "atr_window",
        "sl_multiplier",
        "fixed_size"
    ]
    #变量 而策略变量则在交易的过程中随着策略的状态而变化
    variables = [
        "boll_up",
        "boll_down",
        "cci_value",
        "atr_value",
        "intra_trade_high",
        "intra_trade_low",
        "long_stop",
        "short_stop"
    ]
    # 初始化
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        # K线生成模块，如合成15min钟k线数据
        self.bg = BarGenerator(self.on_bar, 15, self.on_15min_bar)
        # ArrayManager的默认长度为100，size不能小于计算指标的周期长度，用于存储K线数据
        self.am = ArrayManager()
```

on_init：初始化策略，设置参数，创建K线生成模块，创建ArrayManager实例，创建K线数据缓存列表。调用load_bar函数加载历史数据。策略初始化时，策略的inited和trading状态都为【False】，此时只是调用ArrayManager计算并缓存相关的计算指标，不能发出交易信号。调用完on_init函数之后，策略的inited状态才变为【True】，策略初始化才完成。
on_start
on_stop
on_tick:当策略收到最新的Tick数据的行情推送时，on_tick函数会被调用。默认写法是通过BarGenerator的update_tick函数把收到的Tick数据推进前面创建的bg实例中以便合成1分钟的K线，如下方代码所示：
```python
def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)
```
on_bar:当策略收到最新的K线数据时（实盘时默认推进来的是基于Tick合成的一分钟的K线，回测时则取决于选择参数时填入的K线数据频率），on_bar函数就会被调用。示例策略里出现过的写法有两种：
1、如果策略基于on_bar推进来的K线交易，那么请把交易请求类函数都写在on_bar函数下
2、 如果策略需要基于on_bar推进来的K线数据通过BarGenerator合成更长时间周期的K线来交易，那么请在on_bar中调用BarGenerator的update_bar函数，把收到的这个bar推进前面创建的bg实例中即可，如下方代码所示：
```python
def on_bar(self, bar: BarData):
    """
    Callback of new bar data update.
    """
    self.bg.update_bar(bar)
```

首先获取ArrayManager对象，然后将收到的K线推送进去，检查ArrayManager的初始化状态，如果还没初始化成功就直接返回，没有必要去进行后续的交易相关的逻辑判断。因为很多技术指标计算对最少K线数量有要求，如果数量不够的话计算出来的指标会出现错误或无意义。反之，如果没有return，就可以开始计算技术指标了；
```python
am = self.am
am.update_bar(bar)
if not am.inited:
    return
```
self.cancel_all(): 撤销所有未成交的委托单
self.buy: 创建一个买单,买入开仓（Direction：LONG，Offset：OPEN）
self.sell: 创建一个卖单,卖出平仓（Direction：SHORT，Offset：CLOSE）
self.put_event(): 发送事件，通知界面更新

on_trade: 收到策略成交回报时on_trade函数会被调用。

on_order: 收到策略委托回报时on_order函数会被调用。

on_stop_order: 收到策略停止单回报时on_stop_order函数会被调用。

write_log: 记录日志

load_bar:在策略中调用load_bar函数，可以在策略初始化时加载K线数据。貌似不用手动加载数据，会自动加载

self.cta_engine

capital 金额
limit_orders 挂单,字典，key为BACKTESTING.1，value为OrderData,存在direction,price,volume
limit_orders_count 挂单数量 
trades 交易,和limit_orders一样
