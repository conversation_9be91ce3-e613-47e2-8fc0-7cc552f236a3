import talib
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)

from strategies.trade_utils import TradeUtils
from vnpy.trader.constant import Direction


class MaQueStrategy(CtaTemplate):
    """"""

    author = "用Python的交易员"

    fast_window: int = 10
    slow_window: int = 20

    fast_ma0: float = 0.0
    fast_ma1: float = 0.0
    slow_ma0: float = 0.0
    slow_ma1: float = 0.0

    parameters = ["fast_window", "slow_window"]
    variables = ["fast_ma0", "fast_ma1", "slow_ma0", "slow_ma1"]

    no_buy = False
    up_profit = False
    sell_count = 0
    buy_price = 0.0
    no_buy = False

    def on_init(self) -> None:
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")

        self.bg: BarGenerator = BarGenerator(self.on_bar)
        self.am: ArrayManager = ArrayManager(61)
        self.trade_utils = TradeUtils(strategy=self)

        # self.load_bar(100)

    def on_start(self) -> None:
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
        self.put_event()

    def on_stop(self) -> None:
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

        self.put_event()

    def on_tick(self, tick: TickData) -> None:
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)
    def on_bar(self, bar: BarData) -> None:
        """
        Callback of new bar data update.
        """
        self.cancel_all()

        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return
        sma60 = talib.SMA(am.close, timeperiod=60)
        sma20 = talib.SMA(am.close, timeperiod=20)
        sma5 = talib.SMA(am.close, timeperiod=5)
        if self.pos:
            buy_price = self.buy_price
            up_profit = self.up_profit
            sell_count = self.sell_count
            # 跌破止损位，全部卖出
            if bar.close_price < buy_price * 0.975:
                self.trade_utils.sell_share(bar.close_price, 1.0)
                self.no_buy = True
                return
            # 曾经突破过止盈位并且跌破止盈位，全部卖出
            if bar.close_price > buy_price * 1.025 and up_profit:
                self.trade_utils.sell_share(bar.close_price, 1.0)
                return
            # 超过止盈位
            if bar.close_price >= buy_price * 1.025:
                self.up_profit = True
                # 超过止盈位1%，卖出10%
                sell_share = int(
                    ((bar.close_price/buy_price-1.025)*100-sell_count)*10)//10*10
                if sell_share > 0:
                    self.trade_utils.sell_share(bar.close_price, sell_share/100, True)
                    self.sell_count = sell_count + sell_share//10
        # 判断趋势
        buy_signal = am.close[-1] > am.close[-2] and (
            sma5[-1] > sma60[-1] if self.no_buy else sma5[-1] > sma20[-1])
        if not self.pos and buy_signal:
            self.trade_utils.buy_share(bar.close_price, 1.0)
        self.put_event()

    def on_order(self, order: OrderData) -> None:
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        rate = self.cta_engine.rate
        # 计算剩余资金
        if trade.direction == Direction.LONG:
            self.trade_utils.cash -= trade.price * trade.volume*(1+rate)
            self.trade_utils.total_pos = trade.volume
            self.buy_price = trade.price
            self.up_profit = False
            self.sell_count = 0
            self.no_buy = False
        else:
            self.trade_utils.cash += trade.price * trade.volume*(1-rate)
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder) -> None:
        """
        Callback of stop order update.
        """
        pass
