{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 准备数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["xtquant文档地址：http://dict.thinktrader.net/nativeApi/start_now.html\n"]}], "source": ["# 加载模块\n", "from datetime import datetime\n", "\n", "from tqdm import tqdm\n", "from xtquant import xtdata\n", "\n", "from vnpy.trader.database import DB_TZ\n", "from vnpy.trader.datafeed import get_datafeed\n", "from vnpy.trader.constant import Exchange, Interval\n", "from vnpy.trader.object import HistoryRequest\n", "\n", "from vnpy.alpha import AlphaLab, logger"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 设置下载参数\n", "task_name = \"csi300\"\n", "index_symbol = \"000300.SSE\"\n", "xt_index_symbol = \"000300.SH\"\n", "\n", "start_date = \"20070101\"\n", "end_date = \"20231231\"\n", "\n", "intervals = [\n", "    Interval.DAILY,\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 创建投研实验室\n", "lab = AlphaLab(f\"./lab/{task_name}\")    # 指定数据文件夹"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化数据服务（这里配置使用的迅投研）\n", "datafeed = get_datafeed()\n", "datafeed.init()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'000001.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 377},\n", " '000002.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 313},\n", " '000003.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 26},\n", " '000004.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 278},\n", " '000005.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 65},\n", " '000006.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 14, 20, 0, 0, 2000),\n", "  'count': 12},\n", " '000007.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 20, 20, 0, 0, 7000),\n", "  'count': 56},\n", " '000008.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 97},\n", " '000009.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 44},\n", " '000010.BKZS': {'start_time': datetime.datetime(2005, 1, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 70},\n", " '000011.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 147},\n", " '000015.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 20},\n", " '000016.BKZS': {'start_time': datetime.datetime(2004, 1, 2, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 57},\n", " '000017.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 316},\n", " '000018.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 32},\n", " '000019.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 37},\n", " '000020.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 27, 20, 0, 0, 2000),\n", "  'count': 39},\n", " '000021.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000025.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 32},\n", " '000026.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 30},\n", " '000027.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 32},\n", " '000028.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000029.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000030.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 39},\n", " '000031.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 41},\n", " '000032.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000033.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000034.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000035.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 38},\n", " '000036.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000037.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 31},\n", " '000038.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 32},\n", " '000039.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000040.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 30},\n", " '000041.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 34},\n", " '000042.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 36},\n", " '000043.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000044.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 39},\n", " '000045.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 54},\n", " '000046.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 55},\n", " '000047.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 56},\n", " '000048.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 28},\n", " '000049.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000050.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000051.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 42},\n", " '000052.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000053.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 42},\n", " '000054.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 39},\n", " '000055.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 35},\n", " '000056.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000057.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000058.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000059.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 52},\n", " '000060.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 54},\n", " '000062.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 35},\n", " '000063.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 38},\n", " '000064.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000065.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 35},\n", " '000066.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000067.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000068.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000069.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000070.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000071.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000072.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000073.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000074.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000075.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 31},\n", " '000076.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 28},\n", " '000077.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000078.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 32},\n", " '000079.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 31},\n", " '000090.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 271},\n", " '000091.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 39},\n", " '000092.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000093.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 38},\n", " '000094.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000095.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 41},\n", " '000096.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000097.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000098.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 33},\n", " '000099.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 18, 20, 0, 0, 2000),\n", "  'count': 34},\n", " '000100.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 43},\n", " '000102.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000103.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000104.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000105.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 41},\n", " '000106.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 42},\n", " '000107.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000108.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000109.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000110.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000111.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000112.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 29},\n", " '000113.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000114.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 24},\n", " '000115.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 44},\n", " '000117.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000118.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 41},\n", " '000119.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000120.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000121.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000122.BKZS': {'start_time': datetime.datetime(2011, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000123.BKZS': {'start_time': datetime.datetime(2013, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 34},\n", " '000125.BKZS': {'start_time': datetime.datetime(2013, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000126.BKZS': {'start_time': datetime.datetime(2011, 12, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000128.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000129.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000130.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000131.BKZS': {'start_time': datetime.datetime(2012, 2, 9, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 17, 20, 0, 0, 2000),\n", "  'count': 21},\n", " '000132.BKZS': {'start_time': datetime.datetime(2012, 4, 19, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000133.BKZS': {'start_time': datetime.datetime(2012, 4, 19, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 43},\n", " '000134.BKZS': {'start_time': datetime.datetime(2012, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 18},\n", " '000135.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 34},\n", " '000136.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 31},\n", " '000137.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000138.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000139.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 92},\n", " '000141.BKZS': {'start_time': datetime.datetime(2013, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000142.BKZS': {'start_time': datetime.datetime(2013, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000145.BKZS': {'start_time': datetime.datetime(2012, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 30},\n", " '000146.BKZS': {'start_time': datetime.datetime(2012, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000147.BKZS': {'start_time': datetime.datetime(2012, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000148.BKZS': {'start_time': datetime.datetime(2012, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000149.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 31},\n", " '000150.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 30},\n", " '000151.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 18},\n", " '000152.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 19},\n", " '000153.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 20},\n", " '000155.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 38},\n", " '000158.BKZS': {'start_time': datetime.datetime(2012, 9, 28, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000159.BKZS': {'start_time': datetime.datetime(2014, 11, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 20, 20, 0, 0, 2000),\n", "  'count': 90},\n", " '000160.BKZS': {'start_time': datetime.datetime(2015, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 40},\n", " '000161.BKZS': {'start_time': datetime.datetime(2015, 10, 30, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 30},\n", " '000162.BKZS': {'start_time': datetime.datetime(2015, 10, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 27},\n", " '000171.BKZS': {'start_time': datetime.datetime(2017, 1, 26, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 22},\n", " '000300.BKZS': {'start_time': datetime.datetime(2005, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 65},\n", " '000500.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 2},\n", " '000501.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 1, 0, 0),\n", "  'count': 3},\n", " '000510.BKZS': {'start_time': datetime.datetime(2024, 9, 23, 20, 0, 0, 2000),\n", "  'end_time': datetime.datetime(2025, 2, 17, 20, 0, 0, 13000),\n", "  'count': 3},\n", " '000680.BKZS': {'start_time': datetime.datetime(2025, 1, 21, 20, 0, 0, 3000),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 7},\n", " '000681.BKZS': {'start_time': datetime.datetime(2025, 1, 21, 20, 0, 0, 3000),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 7},\n", " '000682.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '000683.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000685.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000687.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '000688.BKZS': {'start_time': datetime.datetime(2020, 7, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 18},\n", " '000689.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000690.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000691.BKZS': {'start_time': datetime.datetime(2024, 3, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 5},\n", " '000692.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 9},\n", " '000693.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000695.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000697.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '000698.BKZS': {'start_time': datetime.datetime(2023, 8, 7, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '000699.BKZS': {'start_time': datetime.datetime(2024, 8, 20, 20, 0, 0, 12000),\n", "  'end_time': datetime.datetime(2025, 3, 17, 20, 0, 0, 2000),\n", "  'count': 4},\n", " '000801.BKZS': {'start_time': datetime.datetime(2011, 11, 7, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000802.BKZS': {'start_time': datetime.datetime(2011, 11, 7, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 46},\n", " '000803.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000804.BKZS': {'start_time': datetime.datetime(2012, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 34},\n", " '000805.BKZS': {'start_time': datetime.datetime(2012, 1, 5, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000806.BKZS': {'start_time': datetime.datetime(2012, 2, 9, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000807.BKZS': {'start_time': datetime.datetime(2012, 2, 16, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000808.BKZS': {'start_time': datetime.datetime(2012, 2, 16, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000809.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 1, 0, 0),\n", "  'count': 10},\n", " '000810.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 30},\n", " '000811.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 29},\n", " '000812.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000813.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000814.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000815.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000816.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000817.BKZS': {'start_time': datetime.datetime(2011, 9, 26, 0, 0),\n", "  'end_time': datetime.datetime(2021, 5, 1, 0, 0),\n", "  'count': 29},\n", " '000818.BKZS': {'start_time': datetime.datetime(2012, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000819.BKZS': {'start_time': datetime.datetime(2012, 5, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000820.BKZS': {'start_time': datetime.datetime(2012, 5, 8, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000821.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 25},\n", " '000822.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000824.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 28},\n", " '000825.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 17},\n", " '000826.BKZS': {'start_time': datetime.datetime(2012, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 18},\n", " '000827.BKZS': {'start_time': datetime.datetime(2012, 9, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 33},\n", " '000828.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 35},\n", " '000829.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 30},\n", " '000830.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000831.BKZS': {'start_time': datetime.datetime(2012, 8, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 35},\n", " '000832.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 144},\n", " '000838.BKZS': {'start_time': datetime.datetime(2012, 9, 28, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000839.BKZS': {'start_time': datetime.datetime(2012, 11, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 36},\n", " '000840.BKZS': {'start_time': datetime.datetime(2012, 11, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000841.BKZS': {'start_time': datetime.datetime(2012, 12, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000842.BKZS': {'start_time': datetime.datetime(2012, 12, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 49},\n", " '000843.BKZS': {'start_time': datetime.datetime(2013, 1, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 36},\n", " '000844.BKZS': {'start_time': datetime.datetime(2013, 1, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000846.BKZS': {'start_time': datetime.datetime(2012, 10, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 1, 0, 0),\n", "  'count': 37},\n", " '000852.BKZS': {'start_time': datetime.datetime(2014, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 43},\n", " '000855.BKZS': {'start_time': datetime.datetime(2014, 11, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 44},\n", " '000859.BKZS': {'start_time': datetime.datetime(2019, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 17},\n", " '000860.BKZS': {'start_time': datetime.datetime(2018, 3, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 28},\n", " '000861.BKZS': {'start_time': datetime.datetime(2019, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 21},\n", " '000888.BKZS': {'start_time': datetime.datetime(2024, 7, 29, 20, 0, 0, 12000),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 49},\n", " '000891.BKZS': {'start_time': datetime.datetime(2023, 6, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 15},\n", " '000901.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 38},\n", " '000902.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 361},\n", " '000903.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 42},\n", " '000904.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 43},\n", " '000905.BKZS': {'start_time': datetime.datetime(2007, 1, 15, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 17, 20, 0, 0, 13000),\n", "  'count': 55},\n", " '000906.BKZS': {'start_time': datetime.datetime(2007, 1, 15, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 72},\n", " '000907.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 51},\n", " '000908.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 26},\n", " '000909.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 31},\n", " '000910.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 34},\n", " '000911.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000912.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 27},\n", " '000913.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '000914.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 41},\n", " '000915.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 30},\n", " '000916.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 20},\n", " '000917.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 26},\n", " '000918.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 38},\n", " '000919.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 36},\n", " '000920.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 40},\n", " '000921.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 40},\n", " '000922.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 21},\n", " '000925.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 23},\n", " '000926.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 1, 0, 0),\n", "  'count': 116},\n", " '000927.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 42},\n", " '000928.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 31},\n", " '000929.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 41},\n", " '000930.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 43},\n", " '000931.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 42},\n", " '000932.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 33},\n", " '000933.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 36},\n", " '000934.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 44},\n", " '000935.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 37},\n", " '000936.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 26},\n", " '000937.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000938.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 1, 0, 0),\n", "  'count': 202},\n", " '000939.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 37},\n", " '000940.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000941.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000942.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 37},\n", " '000943.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 31},\n", " '000944.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 35},\n", " '000945.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 31},\n", " '000946.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 1, 0, 0),\n", "  'count': 10},\n", " '000947.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 21},\n", " '000948.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 34},\n", " '000949.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000950.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 29},\n", " '000951.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 15},\n", " '000952.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 7, 1, 0, 0),\n", "  'count': 27},\n", " '000953.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 1, 0, 0),\n", "  'count': 119},\n", " '000954.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 34},\n", " '000955.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 1, 0, 0),\n", "  'count': 156},\n", " '000956.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 43},\n", " '000957.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 26},\n", " '000958.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 36},\n", " '000959.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 34},\n", " '000960.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 11, 17, 0, 0),\n", "  'count': 9},\n", " '000961.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 29},\n", " '000962.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 40},\n", " '000963.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 37},\n", " '000964.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000965.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 31},\n", " '000966.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 21, 20, 0, 0, 2000),\n", "  'count': 39},\n", " '000967.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 45},\n", " '000968.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 39},\n", " '000969.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 39},\n", " '000970.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 1, 0, 0),\n", "  'count': 34},\n", " '000971.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 43},\n", " '000972.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 37},\n", " '000973.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 4, 14, 0, 0),\n", "  'count': 21},\n", " '000975.BKZS': {'start_time': datetime.datetime(2014, 1, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 25},\n", " '000976.BKZS': {'start_time': datetime.datetime(2014, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2021, 4, 14, 0, 0),\n", "  'count': 10},\n", " '000977.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 32},\n", " '000978.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000979.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 31},\n", " '000980.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 31, 0, 0),\n", "  'count': 29},\n", " '000981.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 42},\n", " '000982.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 17, 20, 0, 0, 13000),\n", "  'count': 51},\n", " '000983.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 7, 1, 0, 0),\n", "  'count': 23},\n", " '000984.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 4, 20, 0, 0, 2000),\n", "  'count': 46},\n", " '000985.BKZS': {'start_time': datetime.datetime(2011, 8, 2, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 95},\n", " '000986.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 39},\n", " '000987.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 61},\n", " '000988.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 67},\n", " '000989.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 66},\n", " '000990.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 42},\n", " '000991.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 50},\n", " '000992.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 12, 20, 0, 0, 12000),\n", "  'count': 52},\n", " '000993.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 55},\n", " '000994.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 33},\n", " '000995.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 3, 0, 0),\n", "  'count': 44},\n", " '000996.BKZS': {'start_time': datetime.datetime(2011, 9, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 4, 14, 0, 0),\n", "  'count': 19},\n", " '000997.BKZS': {'start_time': datetime.datetime(2011, 11, 10, 0, 0),\n", "  'end_time': datetime.datetime(2021, 5, 7, 0, 0),\n", "  'count': 37},\n", " '000998.BKZS': {'start_time': datetime.datetime(2011, 11, 7, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 25, 20, 0, 0, 3000),\n", "  'count': 34},\n", " '101075.BKZS': {'start_time': datetime.datetime(2018, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 116},\n", " '101076.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 14, 20, 0, 0, 12000),\n", "  'count': 280},\n", " '101077.BKZS': {'start_time': datetime.datetime(2020, 3, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 20, 20, 0, 0, 2000),\n", "  'count': 82},\n", " '101078.BKZS': {'start_time': datetime.datetime(2018, 12, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 106},\n", " '101079.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 19},\n", " '101080.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 5, 19, 0, 0),\n", "  'count': 15},\n", " '101081.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 63},\n", " '101082.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 4},\n", " '101083.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 4, 0, 0),\n", "  'count': 8},\n", " '101084.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 23, 8, 50, 0, 13000),\n", "  'count': 127},\n", " '101085.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 191},\n", " '101086.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2021, 12, 14, 0, 0),\n", "  'count': 30},\n", " '101087.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 26, 8, 0, 0, 3000),\n", "  'count': 91},\n", " '101088.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 4, 8, 0, 0, 3000),\n", "  'count': 829},\n", " '101089.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 16, 8, 50, 0, 3000),\n", "  'count': 50},\n", " '101090.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 8, 8, 0, 0, 4000),\n", "  'count': 230},\n", " '101091.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 37},\n", " '101092.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 10, 8, 50, 0, 16000),\n", "  'count': 74},\n", " '101093.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 48},\n", " '101094.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 27, 20, 0, 0, 12000),\n", "  'count': 89},\n", " '101095.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 12, 8, 0, 0, 2000),\n", "  'count': 47},\n", " '101096.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 14},\n", " '101097.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 24, 8, 50, 0, 15000),\n", "  'count': 96},\n", " '101098.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 138},\n", " '101099.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 29, 20, 0, 0, 2000),\n", "  'count': 68},\n", " '101100.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 3, 17, 0, 0),\n", "  'count': 28},\n", " '101101.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 25, 8, 50, 0, 3000),\n", "  'count': 161},\n", " '101102.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 9, 8, 0, 0, 9000),\n", "  'count': 21},\n", " '101103.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 27, 20, 0, 0, 13000),\n", "  'count': 107},\n", " '101104.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'count': 1},\n", " '101105.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 30, 0, 0),\n", "  'count': 11},\n", " '101106.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 195},\n", " '101107.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 237},\n", " '101108.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 27, 8, 50, 0, 3000),\n", "  'count': 69},\n", " '101109.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 14, 20, 0, 0, 12000),\n", "  'count': 158},\n", " '101110.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 27, 8, 50, 0, 3000),\n", "  'count': 75},\n", " '101111.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 4, 30, 0, 0),\n", "  'count': 5},\n", " '101112.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 8, 8, 0, 0, 18000),\n", "  'count': 71},\n", " '101113.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 67},\n", " '101114.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 4},\n", " '101115.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 6, 17, 0, 0),\n", "  'count': 2},\n", " '101116.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 4},\n", " '101117.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 113},\n", " '101118.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2021, 2, 18, 0, 0),\n", "  'count': 3},\n", " '101119.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2023, 6, 29, 0, 0),\n", "  'count': 5},\n", " '101120.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 22, 8, 0, 0, 16000),\n", "  'count': 61},\n", " '101121.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 4, 29, 0, 0),\n", "  'count': 12},\n", " '101122.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 10, 8, 0, 0, 3000),\n", "  'count': 106},\n", " '101123.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 56},\n", " '101124.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 24, 0, 0),\n", "  'count': 15},\n", " '101125.BKZS': {'start_time': datetime.datetime(2020, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 81},\n", " '101126.BKZS': {'start_time': datetime.datetime(2020, 4, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 260},\n", " '101127.BKZS': {'start_time': datetime.datetime(2020, 4, 17, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 23, 0, 0),\n", "  'count': 203},\n", " '101128.BKZS': {'start_time': datetime.datetime(2020, 4, 16, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 15, 20, 0, 0, 2000),\n", "  'count': 133},\n", " '101129.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 441},\n", " '101130.BKZS': {'start_time': datetime.datetime(2020, 4, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 9, 8, 0, 0, 4000),\n", "  'count': 386},\n", " '101131.BKZS': {'start_time': datetime.datetime(2020, 4, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 23, 8, 50, 0, 12000),\n", "  'count': 150},\n", " '101132.BKZS': {'start_time': datetime.datetime(2020, 4, 27, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 104},\n", " '101133.BKZS': {'start_time': datetime.datetime(2020, 4, 27, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 129},\n", " '101134.BKZS': {'start_time': datetime.datetime(2020, 4, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 23, 8, 50, 0, 14000),\n", "  'count': 41},\n", " '101135.BKZS': {'start_time': datetime.datetime(2020, 4, 30, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 72},\n", " '101136.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 17, 0, 0),\n", "  'count': 31},\n", " '101137.BKZS': {'start_time': datetime.datetime(2020, 5, 5, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 25},\n", " '101138.BKZS': {'start_time': datetime.datetime(2017, 10, 20, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 14, 8, 0, 0, 17000),\n", "  'count': 108},\n", " '101139.BKZS': {'start_time': datetime.datetime(2020, 5, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 98},\n", " '101140.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 27, 8, 50, 0, 3000),\n", "  'count': 182},\n", " '101141.BKZS': {'start_time': datetime.datetime(2020, 5, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 185},\n", " '101142.BKZS': {'start_time': datetime.datetime(2020, 5, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 131},\n", " '101143.BKZS': {'start_time': datetime.datetime(2020, 5, 19, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 59},\n", " '101144.BKZS': {'start_time': datetime.datetime(2020, 5, 19, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 51},\n", " '101145.BKZS': {'start_time': datetime.datetime(2020, 5, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 144},\n", " '101146.BKZS': {'start_time': datetime.datetime(2020, 5, 27, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 96},\n", " '101147.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 13, 8, 50, 0, 3000),\n", "  'count': 30},\n", " '101148.BKZS': {'start_time': datetime.datetime(2020, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 3, 20, 0, 0, 12000),\n", "  'count': 134},\n", " '101149.BKZS': {'start_time': datetime.datetime(2020, 6, 5, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 2, 8, 50, 0, 13000),\n", "  'count': 181},\n", " '101150.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 103},\n", " '101151.BKZS': {'start_time': datetime.datetime(2020, 6, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 2, 8, 50, 0, 13000),\n", "  'count': 300},\n", " '101152.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 15, 20, 0, 0, 13000),\n", "  'count': 77},\n", " '101153.BKZS': {'start_time': datetime.datetime(2020, 7, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 2, 8, 50, 0, 13000),\n", "  'count': 72},\n", " '101154.BKZS': {'start_time': datetime.datetime(2020, 7, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 4, 0, 0),\n", "  'count': 13},\n", " '101155.BKZS': {'start_time': datetime.datetime(2020, 7, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 11, 8, 50, 0, 21000),\n", "  'count': 238},\n", " '101156.BKZS': {'start_time': datetime.datetime(2020, 7, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 23, 8, 50, 0, 13000),\n", "  'count': 54},\n", " '101157.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 9, 0, 0),\n", "  'count': 44},\n", " '101158.BKZS': {'start_time': datetime.datetime(2020, 7, 15, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 41},\n", " '101159.BKZS': {'start_time': datetime.datetime(2020, 7, 15, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 15, 8, 50, 0, 2000),\n", "  'count': 29},\n", " '101160.BKZS': {'start_time': datetime.datetime(2020, 7, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 8, 8, 0, 0, 18000),\n", "  'count': 88},\n", " '101161.BKZS': {'start_time': datetime.datetime(2020, 7, 22, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 30, 20, 0, 0, 12000),\n", "  'count': 85},\n", " '101162.BKZS': {'start_time': datetime.datetime(2020, 7, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 18, 8, 0, 0, 16000),\n", "  'count': 102},\n", " '101163.BKZS': {'start_time': datetime.datetime(2020, 7, 24, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 105},\n", " '101164.BKZS': {'start_time': datetime.datetime(2020, 7, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 113},\n", " '101165.BKZS': {'start_time': datetime.datetime(2020, 7, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 122},\n", " '101166.BKZS': {'start_time': datetime.datetime(2020, 8, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 40},\n", " '101167.BKZS': {'start_time': datetime.datetime(2020, 8, 5, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 24, 8, 50, 0, 14000),\n", "  'count': 93},\n", " '101168.BKZS': {'start_time': datetime.datetime(2020, 8, 6, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 3, 0, 0),\n", "  'count': 24},\n", " '101169.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 222},\n", " '101170.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 44},\n", " '101171.BKZS': {'start_time': datetime.datetime(2020, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 123},\n", " '101172.BKZS': {'start_time': datetime.datetime(2020, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 199},\n", " '101173.BKZS': {'start_time': datetime.datetime(2020, 9, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 203},\n", " '101174.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 309},\n", " '101175.BKZS': {'start_time': datetime.datetime(2020, 9, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 48},\n", " '101176.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 166},\n", " '101177.BKZS': {'start_time': datetime.datetime(2020, 10, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 23, 8, 50, 0, 12000),\n", "  'count': 139},\n", " '101178.BKZS': {'start_time': datetime.datetime(2020, 10, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 73},\n", " '101179.BKZS': {'start_time': datetime.datetime(2020, 11, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 19, 8, 50, 0, 12000),\n", "  'count': 72},\n", " '101180.BKZS': {'start_time': datetime.datetime(2020, 11, 27, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 90},\n", " '101181.BKZS': {'start_time': datetime.datetime(2020, 12, 8, 0, 0),\n", "  'end_time': datetime.datetime(2022, 4, 12, 0, 0),\n", "  'count': 26},\n", " '101182.BKZS': {'start_time': datetime.datetime(2020, 12, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 184},\n", " '101183.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 11, 0, 0),\n", "  'count': 12},\n", " '101184.BKZS': {'start_time': datetime.datetime(2017, 6, 16, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'count': 42},\n", " '101185.BKZS': {'start_time': datetime.datetime(2020, 12, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 31},\n", " '101186.BKZS': {'start_time': datetime.datetime(2020, 12, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 14, 8, 50, 0, 13000),\n", "  'count': 111},\n", " '101187.BKZS': {'start_time': datetime.datetime(2021, 1, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 182},\n", " '101188.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 275},\n", " '101189.BKZS': {'start_time': datetime.datetime(2021, 1, 26, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 123},\n", " '101190.BKZS': {'start_time': datetime.datetime(2021, 1, 26, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 9},\n", " '101191.BKZS': {'start_time': datetime.datetime(2021, 2, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 1, 20, 0, 0, 8000),\n", "  'count': 36},\n", " '101192.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 117},\n", " '101193.BKZS': {'start_time': datetime.datetime(2021, 2, 23, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 20, 20, 0, 0, 13000),\n", "  'count': 45},\n", " '101194.BKZS': {'start_time': datetime.datetime(2021, 2, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 233},\n", " '101195.BKZS': {'start_time': datetime.datetime(2021, 3, 9, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 71},\n", " '101196.BKZS': {'start_time': datetime.datetime(2021, 3, 11, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 25, 8, 0, 0, 17000),\n", "  'count': 139},\n", " '101197.BKZS': {'start_time': datetime.datetime(2021, 3, 26, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 15, 8, 0, 0, 3000),\n", "  'count': 142},\n", " '101198.BKZS': {'start_time': datetime.datetime(2021, 3, 30, 0, 0),\n", "  'end_time': datetime.datetime(2024, 4, 24, 0, 0),\n", "  'count': 56},\n", " '101199.BKZS': {'start_time': datetime.datetime(2021, 3, 30, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 5, 0, 0),\n", "  'count': 97},\n", " '101200.BKZS': {'start_time': datetime.datetime(2021, 4, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 13, 8, 0, 0, 3000),\n", "  'count': 316},\n", " '101201.BKZS': {'start_time': datetime.datetime(2021, 4, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 124},\n", " '101202.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 23, 8, 0, 0, 4000),\n", "  'count': 123},\n", " '101203.BKZS': {'start_time': datetime.datetime(2021, 5, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 121},\n", " '101204.BKZS': {'start_time': datetime.datetime(2021, 5, 11, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 70},\n", " '101205.BKZS': {'start_time': datetime.datetime(2021, 5, 15, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 1, 0, 0),\n", "  'count': 112},\n", " '101206.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 6, 20, 0, 0, 2000),\n", "  'count': 86},\n", " '101207.BKZS': {'start_time': datetime.datetime(2021, 5, 19, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 3, 8, 50, 0, 13000),\n", "  'count': 186},\n", " '101208.BKZS': {'start_time': datetime.datetime(2021, 5, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 28, 8, 50, 0, 3000),\n", "  'count': 103},\n", " '101209.BKZS': {'start_time': datetime.datetime(2021, 6, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 29, 8, 0, 0, 3000),\n", "  'count': 280},\n", " '101210.BKZS': {'start_time': datetime.datetime(2021, 6, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 77},\n", " '101211.BKZS': {'start_time': datetime.datetime(2021, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 79},\n", " '101212.BKZS': {'start_time': datetime.datetime(2021, 7, 5, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 84},\n", " '101213.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 21},\n", " '101214.BKZS': {'start_time': datetime.datetime(2021, 7, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 30},\n", " '101215.BKZS': {'start_time': datetime.datetime(2021, 7, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 174},\n", " '101216.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 20, 0, 0),\n", "  'count': 27},\n", " '101217.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2024, 3, 29, 0, 0),\n", "  'count': 277},\n", " '101218.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 73},\n", " '101219.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 18},\n", " '101220.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 97},\n", " '101221.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 15},\n", " '101222.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 12, 0, 0),\n", "  'count': 17},\n", " '101223.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2024, 3, 3, 0, 0),\n", "  'count': 28},\n", " '101224.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 10},\n", " '101225.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 422},\n", " '101226.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 98},\n", " '101227.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 18},\n", " '101228.BKZS': {'start_time': datetime.datetime(2021, 7, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 507},\n", " '101229.BKZS': {'start_time': datetime.datetime(2021, 8, 15, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 375},\n", " '101230.BKZS': {'start_time': datetime.datetime(2021, 8, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 92},\n", " '101231.BKZS': {'start_time': datetime.datetime(2021, 8, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 10, 8, 50, 0, 16000),\n", "  'count': 28},\n", " '101232.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 22},\n", " '101233.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101234.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 3, 3, 0, 0),\n", "  'count': 33},\n", " '101235.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 17, 20, 0, 0, 12000),\n", "  'count': 16},\n", " '101236.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 120},\n", " '101237.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 87},\n", " '101238.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 8, 0, 0),\n", "  'count': 81},\n", " '101239.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 9, 8, 0, 0, 4000),\n", "  'count': 2},\n", " '101240.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 23, 8, 50, 0, 14000),\n", "  'count': 31},\n", " '101241.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 725},\n", " '101242.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101243.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 88},\n", " '101244.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 16, 20, 0, 0, 7000),\n", "  'count': 8},\n", " '101245.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 10},\n", " '101246.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 760},\n", " '101247.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 3},\n", " '101248.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 29, 20, 0, 0, 2000),\n", "  'count': 23},\n", " '101249.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 20},\n", " '101250.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 13},\n", " '101251.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 10},\n", " '101252.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 26, 20, 0, 0, 7000),\n", "  'count': 6},\n", " '101253.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 10, 0, 0),\n", "  'count': 2},\n", " '101254.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 71},\n", " '101255.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 166},\n", " '101256.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 10, 20, 0, 0, 15000),\n", "  'count': 12},\n", " '101257.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 9, 8, 0, 0, 3000),\n", "  'count': 21},\n", " '101258.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 12, 8, 0, 0),\n", "  'count': 3},\n", " '101259.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 93},\n", " '101260.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 5, 18, 0, 0),\n", "  'count': 2},\n", " '101261.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 7, 0, 0),\n", "  'count': 7},\n", " '101262.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 13, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '101263.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 25, 8, 50, 0, 16000),\n", "  'count': 19},\n", " '101264.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101265.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 5, 20, 0, 0, 12000),\n", "  'count': 2},\n", " '101266.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 10},\n", " '101267.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 115},\n", " '101268.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 831},\n", " '101269.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101270.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 80},\n", " '101271.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 6},\n", " '101272.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 5},\n", " '101273.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 124},\n", " '101274.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '101275.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 9, 0, 0),\n", "  'count': 4},\n", " '101276.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 35},\n", " '101277.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 4, 20, 0, 0, 12000),\n", "  'count': 17},\n", " '101278.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 34},\n", " '101279.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 922},\n", " '101280.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101281.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101282.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 594},\n", " '101283.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 20, 20, 0, 0, 2000),\n", "  'count': 26},\n", " '101284.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 19, 20, 0, 0, 2000),\n", "  'count': 5},\n", " '101285.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1026},\n", " '101286.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101287.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 6, 29, 0, 0),\n", "  'count': 3},\n", " '101288.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 26, 20, 0, 0, 2000),\n", "  'count': 23},\n", " '101289.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 16, 20, 0, 0, 2000),\n", "  'count': 16},\n", " '101290.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 143},\n", " '101291.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 3},\n", " '101292.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 31, 8, 0, 0, 14000),\n", "  'count': 19},\n", " '101293.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 16, 20, 0, 0, 12000),\n", "  'count': 5},\n", " '101294.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 10, 19, 0, 0),\n", "  'count': 7},\n", " '101295.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 721},\n", " '101296.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 53},\n", " '101297.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 2, 8, 0, 0, 3000),\n", "  'count': 4},\n", " '101298.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 531},\n", " '101299.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 27, 20, 0, 0, 13000),\n", "  'count': 16},\n", " '101300.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 5, 17, 0, 0),\n", "  'count': 9},\n", " '101301.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 51},\n", " '101302.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 14, 20, 0, 0, 2000),\n", "  'count': 4},\n", " '101303.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 9, 8, 0, 0, 4000),\n", "  'count': 456},\n", " '101304.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 8},\n", " '101305.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 74},\n", " '101306.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 9, 0, 0),\n", "  'count': 3},\n", " '101307.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 63},\n", " '101308.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 33},\n", " '101309.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 22, 0, 0),\n", "  'count': 11},\n", " '101310.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 6, 0, 0),\n", "  'count': 4},\n", " '101311.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 24, 8, 50, 0, 14000),\n", "  'count': 14},\n", " '101312.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 6, 8, 0, 0, 9000),\n", "  'count': 26},\n", " '101313.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 11},\n", " '101314.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 127},\n", " '101315.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 6},\n", " '101316.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 14},\n", " '101317.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 19, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '101318.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 30},\n", " '101319.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 40},\n", " '101320.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101321.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 17, 8, 50, 0, 16000),\n", "  'count': 23},\n", " '101322.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 28, 20, 0, 0, 2000),\n", "  'count': 4},\n", " '101323.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 17},\n", " '101324.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 9, 0, 0),\n", "  'count': 2},\n", " '101325.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 2, 6, 0, 0),\n", "  'count': 4},\n", " '101326.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2021, 10, 4, 0, 0),\n", "  'count': 3},\n", " '101327.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 5, 8, 50, 0, 3000),\n", "  'count': 27},\n", " '101328.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1006},\n", " '101329.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 149},\n", " '101330.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 26, 20, 0, 0, 7000),\n", "  'count': 11},\n", " '101331.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101332.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 23, 8, 0, 0, 4000),\n", "  'count': 27},\n", " '101333.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 2},\n", " '101334.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 28, 20, 0, 0, 2000),\n", "  'count': 23},\n", " '101335.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 6},\n", " '101336.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 9, 0, 0),\n", "  'count': 7},\n", " '101337.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 31, 20, 0, 0, 2000),\n", "  'count': 30},\n", " '101338.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 8, 9, 0, 0),\n", "  'count': 4},\n", " '101339.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 12, 6, 0, 0),\n", "  'count': 4},\n", " '101340.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 3, 7, 0, 0),\n", "  'count': 4},\n", " '101341.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 12, 0, 0),\n", "  'count': 2},\n", " '101342.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 21, 20, 0, 0, 2000),\n", "  'count': 25},\n", " '101343.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 12},\n", " '101344.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101345.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'count': 1},\n", " '101346.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 19, 8, 0, 0, 4000),\n", "  'count': 69},\n", " '101347.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 87},\n", " '101348.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2022, 3, 12, 0, 0),\n", "  'count': 5},\n", " '101349.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 21, 0, 0),\n", "  'count': 3},\n", " '101350.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 7, 18, 0, 0),\n", "  'count': 3},\n", " '101351.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 8, 20, 0, 0, 2000),\n", "  'count': 14},\n", " '101352.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101353.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 8, 0, 0),\n", "  'count': 10},\n", " '101354.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 30, 0, 0),\n", "  'count': 3},\n", " '101355.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'count': 1},\n", " '101356.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '101357.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 6, 8, 50, 0, 16000),\n", "  'count': 9},\n", " '101358.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1041},\n", " '101359.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 5, 0, 0),\n", "  'count': 5},\n", " '101360.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 3},\n", " '101361.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 3, 24, 0, 0),\n", "  'count': 2},\n", " '101362.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 3, 20, 0, 0, 2000),\n", "  'count': 2},\n", " '101363.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 9, 8, 0, 0, 3000),\n", "  'count': 27},\n", " '101364.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 9, 0, 0),\n", "  'count': 6},\n", " '101365.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 6, 8, 50, 0, 16000),\n", "  'count': 14},\n", " '101366.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 58},\n", " '101367.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 83},\n", " '101368.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 11, 8, 50, 0, 16000),\n", "  'count': 12},\n", " '101369.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 87},\n", " '101370.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 2, 4, 0, 0),\n", "  'count': 4},\n", " '101371.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 35},\n", " '101372.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 11, 4, 0, 0),\n", "  'count': 5},\n", " '101373.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101374.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 5, 0, 0),\n", "  'count': 2},\n", " '101375.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'count': 1},\n", " '101376.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 27, 20, 0, 0, 2000),\n", "  'count': 5},\n", " '101377.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 163},\n", " '101378.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101379.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 11, 0, 0),\n", "  'count': 7},\n", " '101380.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 26, 8, 0, 0, 3000),\n", "  'count': 64},\n", " '101381.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1041},\n", " '101382.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 17, 8, 50, 0, 16000),\n", "  'count': 159},\n", " '101383.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 5},\n", " '101384.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 10, 19, 0, 0),\n", "  'count': 4},\n", " '101385.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 24, 20, 0, 0, 12000),\n", "  'count': 68},\n", " '101386.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 2, 8, 0, 0, 3000),\n", "  'count': 32},\n", " '101387.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 11, 0, 0),\n", "  'count': 3},\n", " '101388.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 3, 8, 0, 0, 17000),\n", "  'count': 19},\n", " '101389.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 5, 13, 0, 0),\n", "  'count': 6},\n", " '101390.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 140},\n", " '101391.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2022, 6, 2, 0, 0),\n", "  'count': 2},\n", " '101392.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 12, 20, 0, 0, 2000),\n", "  'count': 24},\n", " '101393.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101394.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 4, 0, 0),\n", "  'count': 3},\n", " '101395.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 43},\n", " '101396.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 24},\n", " '101397.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 99},\n", " '101398.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 14},\n", " '101399.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 8, 0, 0),\n", "  'count': 2},\n", " '101400.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 3, 3, 0, 0),\n", "  'count': 19},\n", " '101401.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101402.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 10, 8, 0, 0, 4000),\n", "  'count': 14},\n", " '101403.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1020},\n", " '101404.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 25, 20, 0, 0, 12000),\n", "  'count': 4},\n", " '101405.BKZS': {'start_time': datetime.datetime(2021, 9, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 35},\n", " '101406.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 17, 20, 0, 0, 12000),\n", "  'count': 28},\n", " '101407.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 1},\n", " '101408.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 24, 8, 50, 0, 14000),\n", "  'count': 63},\n", " '101409.BKZS': {'start_time': datetime.datetime(2021, 9, 7, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 137},\n", " '101410.BKZS': {'start_time': datetime.datetime(2021, 9, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 9, 8, 0, 0, 4000),\n", "  'count': 107},\n", " '101411.BKZS': {'start_time': datetime.datetime(2021, 9, 13, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 10, 8, 0, 0, 3000),\n", "  'count': 34},\n", " '101412.BKZS': {'start_time': datetime.datetime(2021, 9, 15, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 14},\n", " '101413.BKZS': {'start_time': datetime.datetime(2021, 9, 23, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 21, 20, 0, 0, 2000),\n", "  'count': 80},\n", " '101414.BKZS': {'start_time': datetime.datetime(2021, 9, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 99},\n", " '101415.BKZS': {'start_time': datetime.datetime(2021, 9, 24, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 23},\n", " '101416.BKZS': {'start_time': datetime.datetime(2021, 9, 24, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 27, 8, 0, 0, 14000),\n", "  'count': 31},\n", " '101417.BKZS': {'start_time': datetime.datetime(2021, 9, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 16, 8, 50, 0, 2000),\n", "  'count': 6},\n", " '101418.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 289},\n", " '101419.BKZS': {'start_time': datetime.datetime(2021, 11, 2, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 54},\n", " '101420.BKZS': {'start_time': datetime.datetime(2021, 11, 29, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 31, 8, 0, 0, 14000),\n", "  'count': 108},\n", " '101421.BKZS': {'start_time': datetime.datetime(2021, 11, 29, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 19},\n", " '101422.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 937},\n", " '101423.BKZS': {'start_time': datetime.datetime(2021, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 14, 20, 0, 0, 2000),\n", "  'count': 67},\n", " '101424.BKZS': {'start_time': datetime.datetime(2021, 12, 21, 0, 0),\n", "  'end_time': datetime.datetime(2023, 12, 22, 0, 0),\n", "  'count': 10},\n", " '101425.BKZS': {'start_time': datetime.datetime(2021, 12, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 144},\n", " '101426.BKZS': {'start_time': datetime.datetime(2018, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 149},\n", " '101427.BKZS': {'start_time': datetime.datetime(2017, 11, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 16, 8, 50, 0, 3000),\n", "  'count': 140},\n", " '101428.BKZS': {'start_time': datetime.datetime(2022, 1, 12, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 96},\n", " '101429.BKZS': {'start_time': datetime.datetime(2022, 1, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 21, 8, 50, 0, 3000),\n", "  'count': 88},\n", " '101430.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 205},\n", " '101431.BKZS': {'start_time': datetime.datetime(2022, 2, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 95},\n", " '101432.BKZS': {'start_time': datetime.datetime(2017, 11, 24, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 9},\n", " '101433.BKZS': {'start_time': datetime.datetime(2022, 2, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 53},\n", " '101434.BKZS': {'start_time': datetime.datetime(2022, 2, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 46},\n", " '101435.BKZS': {'start_time': datetime.datetime(2022, 3, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 12, 8, 0, 0, 2000),\n", "  'count': 6},\n", " '101436.BKZS': {'start_time': datetime.datetime(2022, 3, 9, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 39},\n", " '101437.BKZS': {'start_time': datetime.datetime(2022, 3, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 29},\n", " '101438.BKZS': {'start_time': datetime.datetime(2022, 3, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 55},\n", " '101439.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 22, 8, 0, 0, 16000),\n", "  'count': 611},\n", " '101440.BKZS': {'start_time': datetime.datetime(2022, 4, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 43},\n", " '101441.BKZS': {'start_time': datetime.datetime(2022, 5, 16, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 21},\n", " '101442.BKZS': {'start_time': datetime.datetime(2022, 5, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 20, 8, 50, 0, 2000),\n", "  'count': 36},\n", " '101443.BKZS': {'start_time': datetime.datetime(2022, 5, 18, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 6},\n", " '101444.BKZS': {'start_time': datetime.datetime(2022, 5, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 37},\n", " '101445.BKZS': {'start_time': datetime.datetime(2022, 5, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 220},\n", " '101446.BKZS': {'start_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 24, 8, 0, 0, 9000),\n", "  'count': 4},\n", " '101447.BKZS': {'start_time': datetime.datetime(2022, 5, 30, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 18, 0, 0),\n", "  'count': 2},\n", " '101448.BKZS': {'start_time': datetime.datetime(2022, 6, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 38},\n", " '101449.BKZS': {'start_time': datetime.datetime(2022, 6, 8, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 12, 8, 0, 0, 2000),\n", "  'count': 35},\n", " '101450.BKZS': {'start_time': datetime.datetime(2022, 6, 9, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 5},\n", " '101451.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 4, 30, 0, 0),\n", "  'count': 11},\n", " '101452.BKZS': {'start_time': datetime.datetime(2018, 4, 13, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 240},\n", " '101453.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 20, 0, 0),\n", "  'count': 14},\n", " '101454.BKZS': {'start_time': datetime.datetime(2022, 6, 13, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 10, 8, 0, 0, 3000),\n", "  'count': 22},\n", " '101455.BKZS': {'start_time': datetime.datetime(2017, 6, 26, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 9, 0, 0),\n", "  'count': 10},\n", " '101456.BKZS': {'start_time': datetime.datetime(2018, 3, 20, 0, 0),\n", "  'end_time': datetime.datetime(2021, 10, 12, 0, 0),\n", "  'count': 20},\n", " '101457.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 94},\n", " '101458.BKZS': {'start_time': datetime.datetime(2017, 6, 22, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 16, 0, 0),\n", "  'count': 9},\n", " '101459.BKZS': {'start_time': datetime.datetime(2022, 6, 20, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 56},\n", " '101460.BKZS': {'start_time': datetime.datetime(2022, 6, 23, 0, 0),\n", "  'end_time': datetime.datetime(2024, 4, 29, 20, 0, 0, 2000),\n", "  'count': 4},\n", " '101461.BKZS': {'start_time': datetime.datetime(2022, 6, 24, 0, 0),\n", "  'end_time': datetime.datetime(2022, 7, 18, 0, 0),\n", "  'count': 3},\n", " '101462.BKZS': {'start_time': datetime.datetime(2022, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 35},\n", " '101463.BKZS': {'start_time': datetime.datetime(2022, 7, 5, 0, 0),\n", "  'end_time': datetime.datetime(2022, 8, 16, 0, 0),\n", "  'count': 2},\n", " '101464.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 60},\n", " '101465.BKZS': {'start_time': datetime.datetime(2022, 7, 13, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 2, 0, 0),\n", "  'count': 3},\n", " '101466.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 609},\n", " '101467.BKZS': {'start_time': datetime.datetime(2022, 7, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 70},\n", " '101468.BKZS': {'start_time': datetime.datetime(2022, 7, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 2, 8, 0, 0, 3000),\n", "  'count': 14},\n", " '101469.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 14, 20, 0, 0, 2000),\n", "  'count': 245},\n", " '101470.BKZS': {'start_time': datetime.datetime(2022, 8, 4, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 3},\n", " '101471.BKZS': {'start_time': datetime.datetime(2022, 8, 5, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 13, 18, 40, 2, 199000),\n", "  'count': 18},\n", " '101472.BKZS': {'start_time': datetime.datetime(2022, 8, 9, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 9, 8, 0, 0, 9000),\n", "  'count': 12},\n", " '101473.BKZS': {'start_time': datetime.datetime(2022, 8, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 14, 8, 50, 0, 13000),\n", "  'count': 5},\n", " '101474.BKZS': {'start_time': datetime.datetime(2022, 8, 11, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 17, 20, 0, 0, 12000),\n", "  'count': 21},\n", " '101475.BKZS': {'start_time': datetime.datetime(2022, 8, 15, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 4},\n", " '101476.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 307},\n", " '101477.BKZS': {'start_time': datetime.datetime(2018, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 21, 20, 0, 0, 16000),\n", "  'count': 177},\n", " '101478.BKZS': {'start_time': datetime.datetime(2018, 4, 27, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 27, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101479.BKZS': {'start_time': datetime.datetime(2017, 6, 27, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 23},\n", " '101480.BKZS': {'start_time': datetime.datetime(2016, 4, 26, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1223},\n", " '101481.BKZS': {'start_time': datetime.datetime(2017, 6, 27, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 43},\n", " '101482.BKZS': {'start_time': datetime.datetime(2017, 6, 12, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 27, 20, 0, 0, 12000),\n", "  'count': 52},\n", " '101483.BKZS': {'start_time': datetime.datetime(2016, 12, 8, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 27, 20, 0, 0, 13000),\n", "  'count': 82},\n", " '101484.BKZS': {'start_time': datetime.datetime(2016, 6, 22, 0, 0),\n", "  'end_time': datetime.datetime(2024, 4, 17, 0, 0),\n", "  'count': 47},\n", " '101485.BKZS': {'start_time': datetime.datetime(2017, 6, 27, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 18, 0, 0),\n", "  'count': 33},\n", " '101486.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 23},\n", " '101487.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 11},\n", " '101488.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 20},\n", " '101489.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 1},\n", " '101490.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 83},\n", " '101491.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 21, 8, 50, 0, 3000),\n", "  'count': 27},\n", " '101492.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 16, 8, 0, 0, 4000),\n", "  'count': 13},\n", " '101493.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 28, 8, 50, 0, 3000),\n", "  'count': 7},\n", " '101494.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 18, 8, 50, 0, 13000),\n", "  'count': 15},\n", " '101495.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 10, 20, 0, 0, 15000),\n", "  'count': 5},\n", " '101496.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 43},\n", " '101497.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 15},\n", " '101498.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 10, 8, 0, 0, 3000),\n", "  'count': 8},\n", " '101499.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 41},\n", " '101500.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 56},\n", " '101501.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 6},\n", " '101502.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 5, 8, 0, 0, 3000),\n", "  'count': 32},\n", " '101503.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 22, 0, 0),\n", "  'count': 8},\n", " '101504.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 8},\n", " '101505.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 14, 0, 0),\n", "  'count': 2},\n", " '101506.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 12, 0, 0),\n", "  'count': 2},\n", " '101507.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 9, 8, 0, 0, 9000),\n", "  'count': 22},\n", " '101508.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 12, 8, 0, 0, 2000),\n", "  'count': 34},\n", " '101509.BKZS': {'start_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 7},\n", " '101510.BKZS': {'start_time': datetime.datetime(2022, 11, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 23},\n", " '101511.BKZS': {'start_time': datetime.datetime(2022, 11, 14, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 27, 8, 0, 0, 3000),\n", "  'count': 5},\n", " '101512.BKZS': {'start_time': datetime.datetime(2022, 11, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 140},\n", " '101513.BKZS': {'start_time': datetime.datetime(2022, 11, 16, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 35},\n", " '101514.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 19, 8, 0, 0, 4000),\n", "  'count': 24},\n", " '101515.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 24},\n", " '101516.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 29},\n", " '101517.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 57},\n", " '101518.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'count': 1},\n", " '101519.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'count': 1},\n", " '101520.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 29, 0, 0),\n", "  'count': 7},\n", " '101521.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2023, 9, 19, 0, 0),\n", "  'count': 2},\n", " '101522.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 16, 20, 0, 0, 7000),\n", "  'count': 7},\n", " '101523.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2023, 12, 15, 0, 0),\n", "  'count': 2},\n", " '101524.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 21, 8, 50, 0, 16000),\n", "  'count': 11},\n", " '101525.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 1, 20, 0, 0, 12000),\n", "  'count': 9},\n", " '101526.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'count': 1},\n", " '101527.BKZS': {'start_time': datetime.datetime(2022, 11, 25, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 27, 0, 0),\n", "  'count': 5},\n", " '101528.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 13},\n", " '101529.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 5, 8, 0, 0, 3000),\n", "  'count': 158},\n", " '101530.BKZS': {'start_time': datetime.datetime(2022, 12, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 36},\n", " '101531.BKZS': {'start_time': datetime.datetime(2022, 12, 4, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 28, 0, 0),\n", "  'count': 7},\n", " '101532.BKZS': {'start_time': datetime.datetime(2022, 12, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 10, 8, 50, 0, 16000),\n", "  'count': 14},\n", " '101533.BKZS': {'start_time': datetime.datetime(2022, 12, 7, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 10},\n", " '101534.BKZS': {'start_time': datetime.datetime(2022, 12, 7, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 29, 20, 0, 0, 2000),\n", "  'count': 11},\n", " '101535.BKZS': {'start_time': datetime.datetime(2022, 12, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 13},\n", " '101536.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 19, 8, 0, 0, 4000),\n", "  'count': 104},\n", " '101537.BKZS': {'start_time': datetime.datetime(2016, 6, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 415},\n", " '101538.BKZS': {'start_time': datetime.datetime(2022, 12, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 13},\n", " '101539.BKZS': {'start_time': datetime.datetime(2022, 12, 27, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 3, 8, 0, 0, 17000),\n", "  'count': 7},\n", " '101540.BKZS': {'start_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 1},\n", " '101541.BKZS': {'start_time': datetime.datetime(2023, 1, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 27, 8, 0, 0, 14000),\n", "  'count': 12},\n", " '101542.BKZS': {'start_time': datetime.datetime(2023, 1, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 17},\n", " '101543.BKZS': {'start_time': datetime.datetime(2023, 1, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 19},\n", " '101544.BKZS': {'start_time': datetime.datetime(2023, 1, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 9},\n", " '101545.BKZS': {'start_time': datetime.datetime(2023, 1, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 14, 8, 50, 0, 3000),\n", "  'count': 85},\n", " '101546.BKZS': {'start_time': datetime.datetime(2023, 2, 7, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 30},\n", " '101547.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'count': 1},\n", " '101548.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'count': 1},\n", " '101549.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 2},\n", " '101550.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 8, 20, 0, 0, 2000),\n", "  'count': 7},\n", " '101551.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 14, 0, 0),\n", "  'count': 2},\n", " '101552.BKZS': {'start_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 11, 0, 0),\n", "  'count': 1},\n", " '101553.BKZS': {'start_time': datetime.datetime(2023, 2, 13, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 16},\n", " '101554.BKZS': {'start_time': datetime.datetime(2023, 2, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 27, 20, 0, 0, 12000),\n", "  'count': 11},\n", " '101555.BKZS': {'start_time': datetime.datetime(2023, 2, 22, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 65},\n", " '101556.BKZS': {'start_time': datetime.datetime(2017, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 7, 8, 50, 0, 3000),\n", "  'count': 256},\n", " '101557.BKZS': {'start_time': datetime.datetime(2023, 3, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 7, 8, 50, 0, 3000),\n", "  'count': 32},\n", " '101558.BKZS': {'start_time': datetime.datetime(2023, 3, 3, 0, 0),\n", "  'end_time': datetime.datetime(2023, 6, 13, 0, 0),\n", "  'count': 18},\n", " '101559.BKZS': {'start_time': datetime.datetime(2023, 3, 17, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 24, 8, 50, 0, 15000),\n", "  'count': 29},\n", " '101560.BKZS': {'start_time': datetime.datetime(2023, 3, 20, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 14},\n", " '101561.BKZS': {'start_time': datetime.datetime(2023, 3, 27, 0, 0),\n", "  'end_time': datetime.datetime(2023, 6, 7, 0, 0),\n", "  'count': 3},\n", " '101562.BKZS': {'start_time': datetime.datetime(2023, 4, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 117},\n", " '101563.BKZS': {'start_time': datetime.datetime(2023, 4, 4, 0, 0),\n", "  'end_time': datetime.datetime(2024, 3, 27, 0, 0),\n", "  'count': 8},\n", " '101564.BKZS': {'start_time': datetime.datetime(2023, 4, 5, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 16},\n", " '101565.BKZS': {'start_time': datetime.datetime(2023, 4, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 23, 8, 50, 0, 12000),\n", "  'count': 25},\n", " '101566.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 438},\n", " '101567.BKZS': {'start_time': datetime.datetime(2023, 4, 13, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 23, 8, 50, 0, 13000),\n", "  'count': 31},\n", " '101568.BKZS': {'start_time': datetime.datetime(2023, 4, 17, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 28, 0, 0),\n", "  'count': 3},\n", " '101569.BKZS': {'start_time': datetime.datetime(2023, 4, 17, 0, 0),\n", "  'end_time': datetime.datetime(2023, 4, 17, 0, 0),\n", "  'count': 1},\n", " '101570.BKZS': {'start_time': datetime.datetime(2023, 4, 28, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 60},\n", " '101571.BKZS': {'start_time': datetime.datetime(2016, 6, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 7, 8, 0, 0, 4000),\n", "  'count': 77},\n", " '101572.BKZS': {'start_time': datetime.datetime(2023, 5, 10, 0, 0),\n", "  'end_time': datetime.datetime(2023, 5, 16, 0, 0),\n", "  'count': 2},\n", " '101573.BKZS': {'start_time': datetime.datetime(2023, 5, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 71},\n", " '101574.BKZS': {'start_time': datetime.datetime(2023, 5, 19, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 67},\n", " '101575.BKZS': {'start_time': datetime.datetime(2023, 5, 21, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 37},\n", " '101576.BKZS': {'start_time': datetime.datetime(2023, 5, 24, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 30, 20, 0, 0, 3000),\n", "  'count': 8},\n", " '101577.BKZS': {'start_time': datetime.datetime(2023, 5, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 13, 8, 0, 0, 3000),\n", "  'count': 22},\n", " '101578.BKZS': {'start_time': datetime.datetime(2023, 5, 26, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 28},\n", " '101579.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 10, 20, 0, 0, 2000),\n", "  'count': 105},\n", " '101580.BKZS': {'start_time': datetime.datetime(2023, 5, 30, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 44},\n", " '101581.BKZS': {'start_time': datetime.datetime(2023, 6, 9, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 26},\n", " '101582.BKZS': {'start_time': datetime.datetime(2023, 6, 10, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 97},\n", " '101583.BKZS': {'start_time': datetime.datetime(2023, 7, 5, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 14},\n", " '101584.BKZS': {'start_time': datetime.datetime(2023, 7, 20, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 25, 8, 0, 0, 17000),\n", "  'count': 19},\n", " '101585.BKZS': {'start_time': datetime.datetime(2023, 7, 26, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 85},\n", " '101586.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 25, 8, 50, 0, 3000),\n", "  'count': 183},\n", " '101587.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 333},\n", " '101588.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 8, 8, 0, 0, 18000),\n", "  'count': 405},\n", " '101589.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 19},\n", " '101590.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 21, 8, 50, 0, 16000),\n", "  'count': 140},\n", " '101591.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 331},\n", " '101592.BKZS': {'start_time': datetime.datetime(2018, 1, 9, 0, 0),\n", "  'end_time': datetime.datetime(2019, 3, 19, 0, 0),\n", "  'count': 36},\n", " '101593.BKZS': {'start_time': datetime.datetime(2018, 1, 11, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 25, 0, 0),\n", "  'count': 19},\n", " '101594.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 186},\n", " '101595.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 9, 20, 0, 0, 3000),\n", "  'count': 192},\n", " '101596.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 31, 8, 0, 0, 14000),\n", "  'count': 97},\n", " '101597.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 12, 0, 0),\n", "  'count': 124},\n", " '101598.BKZS': {'start_time': datetime.datetime(2018, 6, 29, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 363},\n", " '101599.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 66},\n", " '101600.BKZS': {'start_time': datetime.datetime(2018, 3, 18, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 350},\n", " '101601.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 549},\n", " '101602.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 23, 8, 0, 0, 4000),\n", "  'count': 121},\n", " '101603.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 112},\n", " '101604.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 311},\n", " '101605.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 50},\n", " '101606.BKZS': {'start_time': datetime.datetime(2016, 9, 14, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 161},\n", " '101607.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 16, 8, 50, 0, 7000),\n", "  'count': 210},\n", " '101608.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 19, 8, 0, 0, 4000),\n", "  'count': 181},\n", " '101609.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 325},\n", " '101610.BKZS': {'start_time': datetime.datetime(2018, 6, 3, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 23, 8, 50, 0, 12000),\n", "  'count': 33},\n", " '101611.BKZS': {'start_time': datetime.datetime(2017, 7, 5, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 54},\n", " '101612.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 2, 8, 50, 0, 13000),\n", "  'count': 237},\n", " '101613.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 418},\n", " '101614.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 132},\n", " '101615.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 336},\n", " '101616.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 87},\n", " '101617.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 133},\n", " '101618.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 22, 8, 50, 0, 3000),\n", "  'count': 101},\n", " '101619.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 1282},\n", " '101620.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 21, 0, 0),\n", "  'count': 89},\n", " '101621.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 52},\n", " '101622.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 116},\n", " '101623.BKZS': {'start_time': datetime.datetime(2017, 10, 23, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 139},\n", " '101624.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 22, 8, 50, 0, 3000),\n", "  'count': 125},\n", " '101625.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 496},\n", " '101626.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 6, 8, 0, 0, 3000),\n", "  'count': 138},\n", " '101627.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 471},\n", " '101628.BKZS': {'start_time': datetime.datetime(2019, 3, 19, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 13},\n", " '101629.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 156},\n", " '101630.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 260},\n", " '101631.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 46},\n", " '101632.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 7, 13, 0, 0),\n", "  'count': 23},\n", " '101633.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 17, 0, 0),\n", "  'count': 17},\n", " '101634.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 12, 20, 0, 0, 2000),\n", "  'count': 114},\n", " '101635.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 9, 20, 0, 0, 3000),\n", "  'count': 129},\n", " '101636.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 253},\n", " '101637.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 23, 8, 0, 0, 4000),\n", "  'count': 118},\n", " '101638.BKZS': {'start_time': datetime.datetime(2016, 8, 11, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 109},\n", " '101639.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 319},\n", " '101640.BKZS': {'start_time': datetime.datetime(2018, 5, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 9},\n", " '101641.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 261},\n", " '101642.BKZS': {'start_time': datetime.datetime(2016, 8, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 235},\n", " '101643.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 160},\n", " '101644.BKZS': {'start_time': datetime.datetime(2016, 7, 7, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 61},\n", " '101645.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 195},\n", " '101646.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 6, 29, 20, 0, 0, 2000),\n", "  'count': 68},\n", " '101647.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 179},\n", " '101648.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 2, 18, 0, 0),\n", "  'count': 8},\n", " '101649.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 19, 20, 0, 0, 4000),\n", "  'count': 160},\n", " '101650.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 4, 21, 0, 0),\n", "  'count': 102},\n", " '101651.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 14},\n", " '101652.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 6, 8, 50, 0, 16000),\n", "  'count': 191},\n", " '101653.BKZS': {'start_time': datetime.datetime(2018, 3, 19, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 411},\n", " '101654.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 262},\n", " '101655.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 24, 8, 50, 0, 15000),\n", "  'count': 248},\n", " '101656.BKZS': {'start_time': datetime.datetime(2018, 8, 24, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 31, 8, 0, 0, 3000),\n", "  'count': 31},\n", " '101657.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 133},\n", " '101658.BKZS': {'start_time': datetime.datetime(2018, 4, 18, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 10, 20, 0, 0, 2000),\n", "  'count': 88},\n", " '101659.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 8, 8, 0, 0, 18000),\n", "  'count': 189},\n", " '101660.BKZS': {'start_time': datetime.datetime(2018, 3, 20, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 19, 8, 0, 0, 4000),\n", "  'count': 35},\n", " '101661.BKZS': {'start_time': datetime.datetime(2016, 5, 27, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 25, 8, 0, 0, 17000),\n", "  'count': 132},\n", " '101662.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 156},\n", " '101663.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 2, 26, 8, 50, 0, 13000),\n", "  'count': 311},\n", " '101664.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 179},\n", " '101665.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 281},\n", " '101666.BKZS': {'start_time': datetime.datetime(2016, 6, 6, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 109},\n", " '101667.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 359},\n", " '101668.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 9, 20, 0, 0, 7000),\n", "  'count': 28},\n", " '101669.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 1, 15, 0, 0),\n", "  'count': 114},\n", " '101670.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 201},\n", " '101671.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 212},\n", " '101672.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 81},\n", " '101673.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 5, 8, 0, 0, 3000),\n", "  'count': 229},\n", " '101674.BKZS': {'start_time': datetime.datetime(2016, 6, 2, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 27},\n", " '101675.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 5, 6, 0, 0),\n", "  'count': 20},\n", " '101676.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 126},\n", " '101677.BKZS': {'start_time': datetime.datetime(2016, 4, 25, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 162},\n", " '101678.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 96},\n", " '101679.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 4, 13, 0, 0),\n", "  'count': 18},\n", " '101680.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 8, 8, 0, 0, 2000),\n", "  'count': 518},\n", " '101681.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 28, 20, 0, 0, 2000),\n", "  'count': 82},\n", " '101682.BKZS': {'start_time': datetime.datetime(2018, 3, 17, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 27, 20, 0, 0, 13000),\n", "  'count': 33},\n", " '101683.BKZS': {'start_time': datetime.datetime(2017, 5, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 2, 8, 0, 0, 3000),\n", "  'count': 71},\n", " '101684.BKZS': {'start_time': datetime.datetime(2017, 4, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 337},\n", " '101685.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 10, 20, 0, 0, 13000),\n", "  'count': 72},\n", " '101686.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 429},\n", " '101687.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 11, 20, 0, 0, 12000),\n", "  'count': 92},\n", " '101688.BKZS': {'start_time': datetime.datetime(2018, 3, 17, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 61},\n", " '101689.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 89},\n", " '101690.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2022, 11, 28, 0, 0),\n", "  'count': 19},\n", " '101691.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 114},\n", " '101692.BKZS': {'start_time': datetime.datetime(2018, 5, 25, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 18, 20, 0, 0, 7000),\n", "  'count': 5},\n", " '101693.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 18, 20, 0, 0, 2000),\n", "  'count': 32},\n", " '101694.BKZS': {'start_time': datetime.datetime(2017, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 14, 0, 0),\n", "  'count': 23},\n", " '101695.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 105},\n", " '101696.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 19, 20, 0, 0, 2000),\n", "  'count': 46},\n", " '101697.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 24, 8, 50, 0, 15000),\n", "  'count': 113},\n", " '101698.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 363},\n", " '101699.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 16, 20, 0, 0, 2000),\n", "  'count': 148},\n", " '101700.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 3, 26, 0, 0),\n", "  'count': 27},\n", " '101701.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 36},\n", " '101702.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 11, 20, 0, 0, 2000),\n", "  'count': 95},\n", " '101703.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 197},\n", " '101704.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 12, 20, 0, 0, 2000),\n", "  'count': 29},\n", " '101705.BKZS': {'start_time': datetime.datetime(2018, 4, 15, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 62},\n", " '101706.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 18, 0, 0),\n", "  'count': 62},\n", " '101707.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 2, 20, 0, 0, 12000),\n", "  'count': 91},\n", " '101708.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 389},\n", " '101709.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 18, 0, 0),\n", "  'count': 8},\n", " '101710.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 198},\n", " '101711.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 201},\n", " '101712.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 7, 8, 0, 0, 4000),\n", "  'count': 57},\n", " '101713.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 25, 8, 0, 0, 17000),\n", "  'count': 375},\n", " '101714.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 1, 16, 8, 50, 0, 3000),\n", "  'count': 80},\n", " '101715.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 26, 8, 0, 0, 3000),\n", "  'count': 141},\n", " '101716.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 77},\n", " '101717.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 3, 29, 0, 0),\n", "  'count': 37},\n", " '101718.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 148},\n", " '101719.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 4, 25, 8, 0, 0, 17000),\n", "  'count': 150},\n", " '101720.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 285},\n", " '101721.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 19, 20, 0, 0, 2000),\n", "  'count': 51},\n", " '101722.BKZS': {'start_time': datetime.datetime(2017, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 30, 20, 0, 0, 12000),\n", "  'count': 13},\n", " '101723.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 50},\n", " '101724.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 102},\n", " '101725.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 29, 20, 0, 0, 2000),\n", "  'count': 151},\n", " '101726.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 30, 0, 0),\n", "  'count': 111},\n", " '101727.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 221},\n", " '101728.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 194},\n", " '101729.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2021, 11, 26, 0, 0),\n", "  'count': 4},\n", " '101730.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2023, 2, 15, 0, 0),\n", "  'count': 14},\n", " '101731.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 85},\n", " '101732.BKZS': {'start_time': datetime.datetime(2016, 4, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 13},\n", " '101733.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 452},\n", " '101734.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 376},\n", " '101735.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 26, 8, 50, 0, 3000),\n", "  'count': 22},\n", " '101736.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 21, 20, 0, 0, 2000),\n", "  'count': 128},\n", " '101737.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 12, 20, 0, 0, 2000),\n", "  'count': 22},\n", " '101738.BKZS': {'start_time': datetime.datetime(2016, 2, 5, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 73},\n", " '101739.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 298},\n", " '101740.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 134},\n", " '101741.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 7, 11, 20, 0, 0, 2000),\n", "  'count': 53},\n", " '101742.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 300},\n", " '101743.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 22, 8, 50, 0, 2000),\n", "  'count': 253},\n", " '101744.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 11, 21, 20, 0, 0, 2000),\n", "  'count': 102},\n", " '101745.BKZS': {'start_time': datetime.datetime(2017, 5, 31, 0, 0),\n", "  'end_time': datetime.datetime(2022, 5, 16, 0, 0),\n", "  'count': 8},\n", " '101746.BKZS': {'start_time': datetime.datetime(2016, 1, 4, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 492},\n", " '101747.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 10, 14, 20, 0, 0, 2000),\n", "  'count': 277},\n", " '101748.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 6, 26, 0, 0),\n", "  'count': 32},\n", " '101749.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 9, 3, 0, 0),\n", "  'count': 76},\n", " '101750.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 127},\n", " '101751.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 6, 13, 0, 0),\n", "  'count': 74},\n", " '101752.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2023, 1, 3, 0, 0),\n", "  'count': 69},\n", " '101753.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 615},\n", " '101754.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 15, 20, 0, 0, 2000),\n", "  'count': 84},\n", " '101755.BKZS': {'start_time': datetime.datetime(2016, 4, 11, 0, 0),\n", "  'end_time': datetime.datetime(2023, 8, 22, 0, 0),\n", "  'count': 41},\n", " '101756.BKZS': {'start_time': datetime.datetime(2018, 6, 14, 0, 0),\n", "  'end_time': datetime.datetime(2019, 3, 19, 0, 0),\n", "  'count': 4},\n", " '101757.BKZS': {'start_time': datetime.datetime(2017, 7, 21, 0, 0),\n", "  'end_time': datetime.datetime(2023, 11, 25, 0, 0),\n", "  'count': 48},\n", " '101758.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 214},\n", " '101759.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 12, 26, 18, 34, 7, 890000),\n", "  'count': 123},\n", " '101760.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 110},\n", " '101761.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 530},\n", " '101762.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 149},\n", " '101763.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 6, 13, 0, 0),\n", "  'count': 2},\n", " '101764.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 1, 27, 0, 0),\n", "  'count': 58},\n", " '101765.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 82},\n", " '101766.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 9, 5, 20, 0, 0, 2000),\n", "  'count': 86},\n", " '101767.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 5, 8, 0, 0, 3000),\n", "  'count': 345},\n", " '101768.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2024, 8, 23, 20, 0, 0, 16000),\n", "  'count': 60},\n", " '101769.BKZS': {'start_time': datetime.datetime(2016, 5, 6, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 103},\n", " '101770.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2019, 3, 19, 0, 0),\n", "  'count': 4},\n", " '101771.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 78},\n", " '101772.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 5, 4, 8, 0, 0, 3000),\n", "  'count': 115},\n", " '101773.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2021, 11, 26, 0, 0),\n", "  'count': 5},\n", " '101774.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2022, 9, 22, 0, 0),\n", "  'count': 36},\n", " '101775.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 3, 11, 8, 50, 0, 16000),\n", "  'count': 108},\n", " '101776.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 13, 10, 1, 17, 901000),\n", "  'count': 139},\n", " '101777.BKZS': {'start_time': datetime.datetime(2016, 1, 1, 0, 0),\n", "  'end_time': datetime.datetime(2025, 6, 4, 20, 0, 0, 2000),\n", "  'count': 146},\n", " ...}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 下载历史成分股信息\n", "xtdata.download_sector_data()\n", "\n", "xtdata.download_history_data(\"\", \"stocklistchange\", \"\", \"\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 查询交易日历\n", "days = xtdata.get_trading_calendar(market=\"SZ\", start_time=start_date, end_time=end_date)\n", "\n", "# 轮询获取指数成本股\n", "index_components = {}\n", "end_datetime = datetime.strptime(end_date, \"%Y%m%d\")\n", "for i in days:\n", "    dt = datetime.strptime(i, \"%Y%m%d\")\n", "    if dt > end_datetime:\n", "        continue\n", "\n", "    xt_symbols = xtdata.get_stock_list_in_sector(xt_index_symbol, i)\n", "\n", "    vt_symbols: list = []\n", "    for xt_symbol in xt_symbols:\n", "        vt_symbol = xt_symbol.replace(\"SH\", \"SSE\").replace(\"SZ\", \"SZSE\")\n", "        vt_symbols.append(vt_symbol)\n", "\n", "    index_components[dt.strftime(\"%Y-%m-%d\")] = vt_symbols\n", "\n", "# 保存到数据中心\n", "lab.save_component_data(index_symbol, index_components)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 加载指数成分股代码\n", "component_symbols = lab.load_component_symbols(index_symbol, start_date, end_date)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████| 854/854 [01:21<00:00, 10.54it/s]\n"]}], "source": ["# 转换时间格式\n", "start = datetime.strptime(start_date, \"%Y%m%d\")\n", "start.replace(tzinfo=DB_TZ)\n", "\n", "end = datetime.strptime(end_date, \"%Y%m%d\")\n", "end.replace(tzinfo=DB_TZ)\n", "\n", "# 除了成分股，还要下载指数数据\n", "task_symbols = component_symbols + [index_symbol]\n", "\n", "# 遍历下载数据\n", "for vt_symbol in tqdm(task_symbols):\n", "    symbol, exchange_str = vt_symbol.split(\".\")\n", "\n", "    for interval in intervals:\n", "        req = HistoryRequest(symbol, Exchange(exchange_str), start, end, interval)\n", "        bars = datafeed.query_bar_history(req)\n", "\n", "        if bars:\n", "            lab.save_bar_data(bars)\n", "        else:\n", "            logger(interval, vt_symbol)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 添加回测参数配置\n", "for vt_symbol in component_symbols:\n", "    lab.add_contract_setting(\n", "        vt_symbol,\n", "        long_rate=5/10000,\n", "        short_rate=10/10000,\n", "        size=1,\n", "        pricetick=0.0001,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}