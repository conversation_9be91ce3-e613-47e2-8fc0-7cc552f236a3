#!/usr/bin/env python3
"""
测试 trade_utils 装饰器功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from strategies.trade_utils import trade_utils

# 模拟 CTA 引擎和策略基类
class MockCtaEngine:
    def __init__(self):
        self.capital = 100000.0
        self.rate = 0.001

class MockCtaTemplate:
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        self.cta_engine = cta_engine
        self.strategy_name = strategy_name
        self.vt_symbol = vt_symbol
        self.setting = setting
        self.pos = 0.0
        
    def buy(self, price, volume):
        print(f"买入: 价格={price}, 数量={volume}")
        self.pos += volume
        
    def sell(self, price, volume):
        print(f"卖出: 价格={price}, 数量={volume}")
        self.pos -= volume

# 使用装饰器的测试策略
@trade_utils
class TestStrategy(MockCtaTemplate):
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        print(f"初始化完成，trade_cash={self.trade_cash}, trade_total_pos={self.trade_total_pos}")

def test_decorator():
    """测试装饰器功能"""
    print("=== 测试 trade_utils 装饰器 ===")
    
    # 创建模拟对象
    engine = MockCtaEngine()
    strategy = TestStrategy(engine, "test", "AAPL.NASDAQ", {})
    
    # 测试属性是否正确添加
    assert hasattr(strategy, 'trade_cash'), "缺少 trade_cash 属性"
    assert hasattr(strategy, 'trade_total_pos'), "缺少 trade_total_pos 属性"
    assert strategy.trade_cash == 100000.0, f"trade_cash 值错误: {strategy.trade_cash}"
    assert strategy.trade_total_pos == 0.0, f"trade_total_pos 值错误: {strategy.trade_total_pos}"
    
    # 测试方法是否正确添加
    assert hasattr(strategy, 'calc_target_share'), "缺少 calc_target_share 方法"
    assert hasattr(strategy, 'buy_share'), "缺少 buy_share 方法"
    assert hasattr(strategy, 'sell_share'), "缺少 sell_share 方法"
    
    # 测试计算目标交易量
    target_share = strategy.calc_target_share(100.0, 0.5)
    expected = 100000.0 / (100.0 * 1.001) * 0.5
    print(f"计算目标交易量: {target_share}, 期望: {expected}")
    assert abs(target_share - expected) < 0.01, f"计算错误: {target_share} != {expected}"
    
    # 测试买入
    print("\n测试买入:")
    strategy.buy_share(100.0, 0.1)  # 买入10%
    
    # 测试卖出
    print("\n测试卖出:")
    strategy.pos = 500.0  # 设置持仓
    strategy.sell_share(100.0, 0.5)  # 卖出50%
    
    print("\n=== 装饰器测试通过! ===")

if __name__ == "__main__":
    test_decorator()
