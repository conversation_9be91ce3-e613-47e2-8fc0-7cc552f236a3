import talib
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)

from strategies.trade_utils import TradeUtils
from vnpy.trader.constant import Direction

"""
均线策略
"""


class MaStrategy(CtaTemplate):
    """"""

    author = "用Python的交易员"

    fast_window: int = 10
    slow_window: int = 20

    fast_ma0: float = 0.0
    fast_ma1: float = 0.0
    slow_ma0: float = 0.0
    slow_ma1: float = 0.0

    parameters = ["fast_window", "slow_window"]
    variables = ["fast_ma0", "fast_ma1", "slow_ma0", "slow_ma1"]

    def on_init(self) -> None:
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")

        self.bg: BarGenerator = BarGenerator(self.on_bar)
        self.am: ArrayManager = ArrayManager(60)
        self.trade_utils = TradeUtils(strategy=self)

        # self.load_bar(100)

    def on_start(self) -> None:
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
        self.put_event()

    def on_stop(self) -> None:
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

        self.put_event()

    def on_tick(self, tick: TickData) -> None:
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData) -> None:
        """
        Callback of new bar data update.
        """
        self.cancel_all()

        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return
        fast_ma = am.sma(self.fast_window, array=True)
        self.fast_ma0 = fast_ma[-1]

        slow_ma = am.sma(self.slow_window, array=True)
        self.slow_ma0 = slow_ma[-1]

        # 金叉开多，死叉平多开空
        if self.fast_ma0 > self.slow_ma0 and not self.pos:
            self.trade_utils.buy_share(bar.close_price, 1)
        if self.fast_ma0 < self.slow_ma0 and self.pos > 0:
            self.sell(bar.close_price, self.pos)
        self.put_event()

    def on_order(self, order: OrderData) -> None:
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        rate = self.cta_engine.rate
        # 计算剩余资金
        if trade.direction == Direction.LONG:
            self.trade_utils.cash -= trade.price * trade.volume*(1+rate)
        else:
            self.trade_utils.cash += trade.price * trade.volume*(1-rate)
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder) -> None:
        """
        Callback of stop order update.
        """
        pass
