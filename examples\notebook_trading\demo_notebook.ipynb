{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["该Jupyter Notebook用于展示如何使用ScriptTraderApp模块，在CLI命令行下进行交易指令的调用"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from vnpy.trader.utility import load_json\n", "\n", "from vnpy_scripttrader import init_cli_trading\n", "from vnpy_ctp import CtpGateway"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 连接到服务器\n", "setting = load_json(\"connect_ctp.json\")\n", "engine = init_cli_trading([CtpGateway])\n", "engine.connect_gateway(setting, \"CTP\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["# 查询所有合约\n", "engine.get_all_contracts(use_df=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询资金\n", "engine.get_all_accounts(use_df=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询持仓\n", "engine.get_all_positions(use_df=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询活动委托\n", "engine.get_all_active_orders(use_df=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 订阅行情\n", "engine.subscribe([\"sc2209.INE\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询行情\n", "engine.get_tick(\"sc2209.INE\", use_df=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 委托下单\n", "vt_orderid = engine.buy(\"sc2209.INE\", 32, 1000)\n", "print(vt_orderid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询特定委托\n", "engine.get_order(vt_orderid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 委托撤单\n", "engine.cancel_order(vt_orderid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}